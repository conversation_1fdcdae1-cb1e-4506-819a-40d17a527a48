import 'package:flutter/material.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/data/panchang_stats.dart';
import 'package:panchang_at_this_moment/utils.dart';
import 'package:panchang_at_this_moment/utils/countdown_dialog.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class CurrentPanchangItem {
  final String name;
  final DateTime start;
  final DateTime end;

  CurrentPanchangItem(
      {required this.end, required this.start, required this.name});
}

class CurrentItem{
  final String name;

  CurrentItem({required this.name});
}

Widget? getPanchangItemSomeTimeChange(  
    {required PanchangDataForThreeDays panchangData,
    required DateTime currentTime,
    required TextStyle titleTextStyle,
    required TextStyle smallTextStyle,
    required BuildContext context}) {
  final List<Widget> panchangItems = [];
  final List<CurrentItem> panchangSelectedItems = [];
  final List<CurrentPanchangItem> panchangSelectedItemsWithLocalizedNames = [];
  final panchangStats = getPanchangStats(context);
  
  void addPanchangItem({
    required bool isCurrent,
    required String nameKey,
    required String displayName,
    required DateTime start,
    required DateTime end,
  }) {
    if (isCurrent) {
      panchangItems.add(Text(
        displayName,
        style: titleTextStyle.copyWith(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: (panchangStats[nameKey] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: displayName,
        start: start,
        end: end,
      ));
      panchangSelectedItems.add(CurrentItem(name: nameKey));
    }
  }

  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    addPanchangItem(
      isCurrent: _isCurrent(dayData.bramhaMuhrat, currentTime),
      nameKey: 'bramhaMuhrat',
      displayName: AppLocalizations.of(context)!.bramhaMuhrat,
      start: dayData.bramhaMuhrat.start,
      end: dayData.bramhaMuhrat.end,
    );
    if (dayData.abhijit != null) {
      addPanchangItem(
        isCurrent: _isCurrent(dayData.abhijit!, currentTime),
        nameKey: 'abhijit',
        displayName: AppLocalizations.of(context)!.abhijit,
        start: dayData.abhijit!.start,
        end: dayData.abhijit!.end,
      );
    }
    addPanchangItem(
      isCurrent: _isCurrent(dayData.godhuli, currentTime),
      nameKey: 'godhuli',
      displayName: AppLocalizations.of(context)!.godhuli,
      start: dayData.godhuli.start,
      end: dayData.godhuli.end,
    );
    addPanchangItem(
      isCurrent: _isCurrent(dayData.pratahSandhya, currentTime),
      nameKey: 'pratahSandhya',
      displayName: AppLocalizations.of(context)!.pratahSandhya,
      start: dayData.pratahSandhya.start,
      end: dayData.pratahSandhya.end,
    );
    addPanchangItem(
      isCurrent: _isCurrent(dayData.vijayMuhurat, currentTime),
      nameKey: 'vijayMuhurat',
      displayName: AppLocalizations.of(context)!.vijayMuhurat,
      start: dayData.vijayMuhurat.start,
      end: dayData.vijayMuhurat.end,
    );
    addPanchangItem(
      isCurrent: _isCurrent(dayData.sayahnaSandhya, currentTime),
      nameKey: 'sayahnaSandhya',
      displayName: AppLocalizations.of(context)!.sayahnaSandhya,
      start: dayData.sayahnaSandhya.start,
      end: dayData.sayahnaSandhya.end,
    );
    addPanchangItem(
      isCurrent: _isCurrent(dayData.nishitaMuhurta, currentTime),
      nameKey: 'nishitaMuhurta',
      displayName: AppLocalizations.of(context)!.nishitaMuhurta,
      start: dayData.nishitaMuhurta.start,
      end: dayData.nishitaMuhurta.end,
    );
    addPanchangItem(
      isCurrent: _isCurrent(dayData.rahuKal, currentTime),
      nameKey: 'rahuKal',
      displayName: AppLocalizations.of(context)!.rahuKal,
      start: dayData.rahuKal.start,
      end: dayData.rahuKal.end,
    );
    addPanchangItem(
      isCurrent: _isCurrent(dayData.gulikaiKal, currentTime),
      nameKey: 'gulikaiKal',
      displayName: AppLocalizations.of(context)!.gulikaiKal,
      start: dayData.gulikaiKal.start,
      end: dayData.gulikaiKal.end,
    );
    addPanchangItem(
      isCurrent: _isCurrent(dayData.yamaganda, currentTime),
      nameKey: 'yamaganda',
      displayName: AppLocalizations.of(context)!.yamaganda,
      start: dayData.yamaganda.start,
      end: dayData.yamaganda.end,
    );
    if (dayData.durMuhurtam.any((element) => _isCurrent(element, currentTime))) {
      final current = dayData.durMuhurtam.firstWhere((element) => _isCurrent(element, currentTime));
      addPanchangItem(
        isCurrent: true,
        nameKey: 'durMuhurtam',
        displayName: AppLocalizations.of(context)!.durMuhurtam,
        start: current.start,
        end: current.end,
      );
    }
    if (dayData.varjyam.any((element) => _isCurrent(element, currentTime))) {
      final current = dayData.varjyam.firstWhere((element) => _isCurrent(element, currentTime));
      addPanchangItem(
        isCurrent: true,
        nameKey: 'varjyam',
        displayName: AppLocalizations.of(context)!.varjyam,
        start: current.start,
        end: current.end,
      );
    }
    if (dayData.amritKal.any((element) => _isCurrent(element, currentTime))) {
      final current = dayData.amritKal.firstWhere((element) => _isCurrent(element, currentTime));
      addPanchangItem(
        isCurrent: true,
        nameKey: 'amritKal',
        displayName: AppLocalizations.of(context)!.amritKal,
        start: current.start,
        end: current.end,
      );
    }
  }

  List<String> panchangTips = panchangSelectedItems
      .map((item) =>
          ((panchangStats[item.name] as Map)["displayMessage"] as String))
      .toList();

  List<double> goodList = panchangSelectedItems
      .where(
          (item) => ((panchangStats[item.name] as Map)["isPositive"] as bool))
      .map((item) => ((panchangStats[item.name] as Map)["score"] as num).toDouble())
      .toList();
  double good = (goodList.isEmpty) ? 0 : goodList.reduce((a, b) => a + b);

  List<double> badList = panchangSelectedItems
      .where((item) => !(panchangStats[item.name] as Map)["isPositive"])
      .map((item) => ((panchangStats[item.name] as Map)["score"] as num).toDouble())
      .toList();
  double bad = (badList.isEmpty) ? 0 : badList.reduce((a, b) => a + b);

  double total = good + bad * -1;
  double goodRatio = (good / total) * 100;
  double badRatio = (bad * -1 / total) * 100;

  return (panchangItems.isNotEmpty)
      ? CountDownWithTimeDialog(
          showMinutesFormattedTime: true,
          currentTime: currentTime,
          countDownItems: panchangSelectedItemsWithLocalizedNames
              .map((item) => CountDownItem(
                    title: item.name,
                    endTime: item.end,
                    startTime: item.start,
                  ))
              .toList(),
          child: Container(
            color: Colors.transparent,
            child: Column(
              children: [
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Column(
                        children: panchangItems,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Column(
                        children: [
                          Text(
                            "+ve score : ${bad + good}",
                            style: smallTextStyle,
                          ),
                          const SizedBox(
                            height: 2,
                          ),
                          GoodAndBadCircle(
                            good: goodRatio,
                            bad: badRatio,
                          ),
                        ],
                      ),
                    ]),
                const SizedBox(
                  height: 10,
                ),
                CirculatingText(
                  texts: panchangTips,
                )
              ],
            ),
          ))
      : null;
}

bool _isCurrent(PanchangSomeTimeItem interval, DateTime currentTime) {
  return interval.start.isBefore(currentTime) &&
      interval.end.isAfter(currentTime);
}
