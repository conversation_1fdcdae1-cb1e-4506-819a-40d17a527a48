import 'package:flutter/material.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SunMoonCombinedWidget extends StatelessWidget {
  final DateTime? sunrise;
  final DateTime? sunset;
  final DateTime? moonrise;
  final DateTime? moonset;

  const SunMoonCombinedWidget({
    super.key,
    this.sunrise,
    this.sunset,
    this.moonrise,
    this.moonset,
  });

  Widget _buildTimeItem(String label, DateTime? time, Color iconColor, IconData directionIcon, BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 13,
                color: iconColor,
                fontWeight: FontWeight.w600,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            time == null
                ? const SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        directionIcon,
                        size: 12,
                        color: iconColor,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        timeFormatter(time),
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.only(left:4, right: 4, bottom: 8),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFFF8E1), Color(0xFFE8EAF6)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          // Sun Section
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.wb_sunny,
                      size: 16,
                      color:  Color.fromARGB(255, 255, 111, 0),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Sun',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: const Color.fromARGB(255, 255, 111, 0),
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    _buildTimeItem(
                      AppLocalizations.of(context)!.sunrise,
                      sunrise,
                      const Color.fromARGB(255, 255, 111, 0),
                      Icons.arrow_upward,
                      context,
                    ),
                    const SizedBox(width: 4),
                    _buildTimeItem(
                      AppLocalizations.of(context)!.sunset,
                      sunset,
                      const Color.fromARGB(255, 255, 111, 0),
                      Icons.arrow_downward,
                      context,
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Divider
          Container(
            width: 1.5,
            height: 60,
            margin: const EdgeInsets.symmetric(horizontal: 6),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.orange.withValues(alpha: 0.4),
                  Colors.indigo.withValues(alpha: 0.4),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(1),
            ),
          ),

          // Moon Section
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.nightlight_round,
                      size: 16,
                      color: Colors.indigo,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Moon',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.indigo,
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    _buildTimeItem(
                      AppLocalizations.of(context)!.moonRise,
                      moonrise,
                      Colors.indigo,
                      Icons.arrow_upward,
                      context,
                    ),
                    const SizedBox(width: 4),
                    _buildTimeItem(
                      AppLocalizations.of(context)!.moonSet,
                      moonset,
                      Colors.indigo,
                      Icons.arrow_downward,
                      context,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

List<Widget> getStaticPanchangItem(PanchangDataForThreeDays? panchangData, DateTime currentTime, BuildContext context) {
  if (panchangData == null) {
    return const [
      Center(child: CircularProgressIndicator()),
    ];
  }

  // The logic remains exactly the same as before
  DateTime? sunrise;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (dayData.sunrise.isAfter(currentTime) &&
        sunrise == null) {
      sunrise = dayData.sunrise;
      break;
    }
  }

  DateTime? sunset;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (sunrise != null && dayData.sunset.isAfter(sunrise)) {
      sunset = dayData.sunset;
      break;
    }
  }

  DateTime? moonrise;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (dayData.moonrise.isAfter(currentTime.add(const Duration(hours: 3))) &&
        moonrise == null) {
      moonrise = dayData.moonrise;
      break;
    }
  }

  DateTime? moonset;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (moonrise != null && dayData.moonset.isAfter(moonrise)) {
      moonset = dayData.moonset;
      break;
    }
  }

  return [
    SunMoonCombinedWidget(
      sunrise: sunrise,
      sunset: sunset,
      moonrise: moonrise,
      moonset: moonset,
    ),
    Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.brown.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.calendar_month, color: Colors.brown, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    AppLocalizations.of(context)!.week,
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      fontSize: 16,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    panchangData.currentDay.weekday,
                    style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                      fontSize: 20,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  ];
}